package com.dh.message.bean.entity;

import com.baomidou.mybatisplus.annotation.*;
import com.dh.message.config.EntityTypeHandler;
import com.dh.message.config.JsonTypeHandler;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <p>
 * 任务监控表
 * </p>
 *
 * <AUTHOR>
 * @since 2025-07-24
 */
@Data
@TableName(value = "task_monitor", autoResultMap = true)
@ApiModel(value="TaskMonitor对象", description="任务监控表")
public class TaskMonitor implements Serializable {

    private static final long serialVersionUID=1L;

    @TableId(value = "id", type = IdType.ID_WORKER)
    private Long id;

    /**
     * 任务监控配置ID
     */
    @ApiModelProperty(value = "任务监控配置ID")
    @TableField("tms_id")
    private Long tmsId;

    /**
     * 业务类型
     */
    @ApiModelProperty(value = "业务类型")
    @TableField("business_type")
    private String businessType;

    /**
     * 业务ID
     */
    @ApiModelProperty(value = "业务ID")
    @TableField("business_id")
    private Long businessId;

    /**
     * 业务扩展ID
     */
    @ApiModelProperty(value = "业务扩展ID")
    @TableField("business_ext_id")
    private Long businessExtId;

    /**
     * 起始时间
     */
    @ApiModelProperty(value = "起始时间")
    @TableField("start_date")
    private LocalDateTime startDate;

    /**
     * 结束时间
     */
    @ApiModelProperty(value = "结束时间")
    @TableField("end_date")
    private LocalDateTime endDate;

    /**
     * 扩展参数
     */
    @ApiModelProperty(value = "扩展参数")
    @TableField(value = "ext_params", typeHandler = JsonTypeHandler.class)
    private Map<String, Object> extParams;

    /**
     * 任务状态(“未开始”、“进行中”、“已完成”、“已取消”、“已逾期”)
     */
    @ApiModelProperty(value = "任务状态(“未开始”、“进行中”、“已完成”、“已取消”、“已逾期”)")
    @TableField("status")
    private Integer status;

    /**
     * 任务完成度
     */
    @ApiModelProperty(value = "任务完成度")
    @TableField("rate")
    private Double rate;

    /**
     * 直接负责人id，多个以,分割
     */
    @ApiModelProperty(value = "直接负责人id，多个以,分割")
    @TableField("related_uid")
    private String relatedUid;

    /**
     * 项目主管id，多个以,分割
     */
    @ApiModelProperty(value = "项目主管id，多个以,分割")
    @TableField("project_manager_uid")
    private String projectManagerUid;

    /**
     * 任务内容
     */
    @ApiModelProperty(value = "任务内容")
    @TableField("task_content")
    private String taskContent;

    /**
     * 部门负责人
     */
    @ApiModelProperty(value = "部门负责人")
    @TableField("dept_manager_uid")
    private Long deptManagerUid;

    /**
     * 日期节点
     */
    @ApiModelProperty(value = "日期节点")
    @TableField(value="date_nodes", typeHandler = EntityTypeHandler.LocalDateTimeMapTypeHandler.class)
    private Map<String, LocalDateTime> dateNodes;

    /**
     * 报警的触发状态
     */
    @ApiModelProperty(value = "报警的触发状态")
    @TableField("alert_flag")
    private Integer alertFlag;

    /**
     * 删除标识(0: 否，1:是)
     */
    @ApiModelProperty(value = "删除标识(0: 否，1:是)")
    @TableField("del_flag")
    @TableLogic
    private Integer delFlag;

    /**
     * 更新人
     */
    @ApiModelProperty(value = "更新人")
    @TableField(value = "update_by", fill = FieldFill.INSERT_UPDATE)
    private Long updateBy;

    /**
     * 更新时间
     */
    @ApiModelProperty(value = "更新时间")
    @TableField(value = "update_date", fill = FieldFill.INSERT_UPDATE)
    private LocalDateTime updateDate;

    /**
     * 创建人
     */
    @ApiModelProperty(value = "创建人")
    @TableField(value = "create_by", fill = FieldFill.INSERT)
    private Long createBy;

    /**
     * 创建时间
     */
    @ApiModelProperty(value = "创建时间")
    @TableField(value = "create_date", fill = FieldFill.INSERT)
    private LocalDateTime createDate;

    /**
     * 获取直接责任人的用户Id
     * @Author: zgj
     * @Date: 2025/7/26 下午7:05
     * @return: java.util.List<java.lang.Long>
     **/
    public List<Long> getUserIdList() {
        return Arrays.stream(relatedUid.split(","))
                .map(Long::parseLong)
                .collect(Collectors.toList());
    }

    /**
     * 获得提醒配置参数
     * @Author: zgj
     * @Date: 2025/7/28 下午3:28
     * @return: java.util.Map<java.lang.String,java.lang.String>
     **/
    public Map<String, String> gainExtParam() {
        Map<String, String> params = new HashMap<>();
        if (extParams == null) {
            return params;
        }
        for (Map.Entry<String, Object> stringObjectEntry : extParams.entrySet()) {
            if (stringObjectEntry.getValue() != null) {
                params.put(stringObjectEntry.getKey(), stringObjectEntry.getValue().toString());
            }
        }
        return params;
    }
}
