package com.dh.message.bean.vo;


import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
public class MonitorRemindVO {

    /**
     * 分级预警
     */
    @ApiModelProperty(value = "分级预警")
    private String level;

    /**
     * 预警内容
     */
    @ApiModelProperty(value = "预警内容")
    private String content;

    /**
     * 预警规则
     */
    @ApiModelProperty(value = "预警规则")
    private String ruleExplain;

    /**
     * 提醒范围
     */
    @ApiModelProperty(value = "提醒范围")
    private String remindScale;
}
