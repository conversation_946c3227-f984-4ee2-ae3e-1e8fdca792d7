package com.dh.message.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.dh.message.entity.SysUser;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <p>
 * 系统用户 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2025-8-4
 */
public interface SysUserMapper extends BaseMapper<SysUser> {

    /**
     * 根据用户ID列表查询邮箱列表
     *
     * @param uids 用户ID列表
     * @return 邮箱列表
     */
    List<String> getEmailsByUids(@Param("uids") List<Long> uids);
}
