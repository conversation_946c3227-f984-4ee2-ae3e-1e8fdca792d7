package com.dh.message.listener;

import com.dh.message.bean.entity.TaskMonitor;
import com.dh.message.bean.entity.TaskMonitorAlert;
import com.dh.message.enums.TaskMonitorStatusEnum;
import com.dh.message.dto.TaskMonitorAlertHistoryDTO;
import com.dh.message.listener.event.TaskMonitorAlertEvent;

import com.dh.message.service.Notification;
import com.dh.message.service.TaskMonitorAlertService;
import com.dh.message.service.TaskMonitorHistoryService;
import com.dh.message.service.TaskMonitorService;
import lombok.extern.slf4j.Slf4j;

import org.springframework.context.event.EventListener;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.List;

/**
 * 报警事件侦听器
 *
 * <AUTHOR>
 * @version 0.9.1
 * @since 2025/7/25
 */
@Component
@Slf4j
public class TaskMonitorAlertListener {
    @Resource
    private Notification notification;

    @Resource
    private TaskMonitorAlertService taskMonitorAlertService;

    @Resource
    private TaskMonitorService taskMonitorService;

    @Resource
    private TaskMonitorHistoryService taskMonitorHistoryService;

    @Resource
    private TaskMonitorEventPublisher taskMonitorEventPublisher;

    @EventListener
    @Transactional(propagation = Propagation.REQUIRES_NEW, noRollbackFor = IllegalArgumentException.class)
    public void handleEvent(TaskMonitorAlertEvent event) {
        log.debug("TaskMonitorListener {} handleEvent {}", event.getTriggerAlertDTO().getIsWarning() ? "warning" :
                        "alert",
                event.getTriggerAlertDTO().getTaskMonitorInfoDTO().getAlarmLevels());
        try {
            //发送通知
            TaskMonitorAlertHistoryDTO taskMonitorAlertHistoryDTO = TaskMonitorAlertHistoryDTO.builder()
                    .tmId(event.getTriggerAlertDTO().getTaskMonitorInfoDTO().getId())
                    .tmsId(event.getTriggerAlertDTO().getTaskMonitorInfoDTO().getTmsId())
                    .level(event.getTriggerAlertDTO().getLevel())
                    .isWarning(event.getTriggerAlertDTO().getIsWarning())
                    .isEmptyDate(event.getTriggerAlertDTO().getIsSkip())
                    .oldAlertFlag(event.getTriggerAlertDTO().getTaskMonitorInfoDTO().getAlertFlag())
                    .alertFlag(event.getTriggerAlertDTO().getAlertFlag())
                    .build();

            if (!event.getTriggerAlertDTO().getIsSkip() || !event.getTriggerAlertDTO().getIsWarning()) { //非报警
                //空日期跳过发送报警/以及报警记录
                List<Long> ccUsers = this.notification.send(event.getTriggerAlertDTO());

                //添加告警信息
                TaskMonitorAlert taskMonitorAlert = taskMonitorAlertService.add(event.getTriggerAlertDTO(), ccUsers);
                taskMonitorAlertHistoryDTO.setTaskMonitorAlertId(taskMonitorAlert.getId());
            }

            //更新任务信息
            TaskMonitor taskMonitor = new TaskMonitor();
            taskMonitor.setId(event.getTriggerAlertDTO().getTaskMonitorInfoDTO().getId());
            taskMonitor.setAlertFlag(event.getTriggerAlertDTO().getAlertFlag());
            if (event.getTriggerAlertDTO().getLevel() == 1 && !event.getTriggerAlertDTO().getIsWarning()) { //1级任务逾期
                //记录状态更改.
                taskMonitorAlertHistoryDTO.setOldStatus(event.getTriggerAlertDTO().getTaskMonitorInfoDTO().getStatus());
                taskMonitorAlertHistoryDTO.setStatus(TaskMonitorStatusEnum.STATUS_OVERDUE.getCode());

                taskMonitor.setStatus(TaskMonitorStatusEnum.STATUS_TERMINATE.getCode());    //所有报警都完成
            }

            taskMonitorHistoryService.addAlertHistory(taskMonitorAlertHistoryDTO);
            taskMonitorService.updateById(taskMonitor);
        } catch (Exception e) {
            log.error("TaskMonitorAlertListener error", e);
        }
    }
}
