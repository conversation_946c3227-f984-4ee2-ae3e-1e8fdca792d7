package com.dh.message.listener;

import com.dh.dto.bean.dto.message.QueueAlertDTO;
import com.dh.message.dto.TriggerAlertDTO;
import com.dh.message.listener.event.QueueAlertEvent;
import com.dh.message.listener.event.TaskMonitorAlertEvent;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.ApplicationEventPublisher;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

/**
 * 事件发布器
 *
 * <AUTHOR>
 * @version 0.9.1
 * @since 2025/7/25
 */
@Service
@Slf4j
public class TaskMonitorEventPublisher {
    @Resource
    private ApplicationEventPublisher eventPublisher;

    /**
     * 触发报警
     *
     * @param triggerAlertDTO 触发报警参数
     */
    public void triggerAlert(TriggerAlertDTO triggerAlertDTO) {
        log.debug("trigger alert event {}", triggerAlertDTO.getLevel());

        TaskMonitorAlertEvent event = new TaskMonitorAlertEvent(this, triggerAlertDTO);
        eventPublisher.publishEvent(event);
    }

    /**
     * 触发报警后触发推送队列事件
     * @param queueAlertDTO 触发报警参数
     */
    public void triggerQueueAlert(QueueAlertDTO queueAlertDTO) {
        log.debug("trigger queue alert event {}", queueAlertDTO.getLevel());

        QueueAlertEvent event = new QueueAlertEvent(this, queueAlertDTO);

        eventPublisher.publishEvent(event);
    }
}
