package com.dh.message.task;

import com.dh.message.dto.TaskMonitorInfoDTO;
import com.dh.message.service.ISysUserService;
import com.dh.message.service.InMemoryLockService;
import com.dh.message.service.TaskMonitorService;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.extern.slf4j.Slf4j;
import org.apache.logging.log4j.util.Strings;
import org.springframework.scheduling.annotation.EnableScheduling;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.ForkJoinPool;

/**
 * 任务监控 Crontab
 *
 * <AUTHOR>
 * @version 0.9.1
 * @since 2025/7/25
 */
@Slf4j
@Component
@EnableScheduling
public class TaskMonitorJob {
    /**
     * 批量数量
     */
    private static final int BATCH_SIZE = 300;

    /**
     * 并行流线程池大小
     */
    private static final int PARALLEL_THREAD_COUNT = 20;

    /**
     * 内存锁
     */
    public static final String LOCK_KEY = "TASK_MONITOR";

    @Resource
    private TaskMonitorService taskMonitorService;

    @Resource
    private InMemoryLockService inMemoryLockService;

    /**
     * 尝试加锁
     *
     * @return boolean
     */
    private boolean tryLock() throws InterruptedException {
        log.debug("TaskMonitorJob 进入加锁");
        return inMemoryLockService.tryLock(LOCK_KEY, 3600 * 1000);
    }

    /**
     * 解锁
     */
    private void unlock() {
        log.debug("TaskMonitorJob 任务解锁");
        inMemoryLockService.unlock(LOCK_KEY);
    }

    @Scheduled(cron = "*/5 * * * * ?")
    //@XxlJob("TaskMonitorJob")
    public void execute() {
        //log.info("TaskMonitorJob execute Start");
        long processCount = 0;
        long totalCount = 0;

        try {
            if (tryLock()) {
                int offset = 0;
                totalCount = taskMonitorService.getInProgressTaskCount();
                log.info("TaskMonitorJob 开始分批处理未完成任务，总任务数: {}", totalCount);
                while (offset < totalCount) {
                    List<TaskMonitorInfoDTO> tasks = taskMonitorService.getInProgressTask(BATCH_SIZE, offset);

                    if (tasks.isEmpty()) {
                        log.info("没有更多任务需要处理，offset: {}", offset);
                        break;
                    }
                    processCount += tasks.size();

                    log.info("TaskMonitorJob 处理第{}批任务，数量: {}", (offset / BATCH_SIZE) + 1, tasks.size());

                    // 使用自定义线程池的并行流处理任务，添加异常处理和性能监控
                    long startTime = System.currentTimeMillis();
                    long successCount = new ForkJoinPool(PARALLEL_THREAD_COUNT).submit(() ->
                            tasks.parallelStream()
                                    .mapToLong(task -> {
                                        try {
                                            taskMonitorService.checkTask(task);
                                            return 1;
                                        } catch (Exception e) {
                                            log.error("处理任务失败，任务ID: {}, 错误: {}", task.getId(), e.getMessage(), e);
                                            return 0;
                                        }
                                    })
                                    .sum()
                    ).join();
                    long endTime = System.currentTimeMillis();
                    
                    log.info("第{}批任务处理完成，成功: {}, 耗时: {}ms", 
                            (offset / BATCH_SIZE) + 1, successCount, endTime - startTime);

                    offset += BATCH_SIZE;
                }
            }
        } catch (InterruptedException e) {
            log.error("TaskMonitorJob execute InterruptedException: ", e);
        } finally {
            unlock();
            log.info("TaskMonitorJob 分批处理完成，总任务: {}, 共处理 {} 个任务", totalCount, processCount);
        }
    }
}
