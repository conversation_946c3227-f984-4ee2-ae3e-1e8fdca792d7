package com.dh.message.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.dh.message.entity.SysUser;

import java.util.List;

/**
 * <p>
 * 系统用户 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-8-4
 */
public interface ISysUserService extends IService<SysUser> {
    /**
     * 根据用户ID列表查询邮箱列表
     *
     * @param uids 用户ID列表
     * @return Email地址列表
     */
    List<String> getEmailsByUids(List<Long> uids);
}
