package com.dh.message.service.impl;

import com.dh.message.bean.entity.TaskMonitorHistory;
import com.dh.message.dto.TaskMonitorAlertHistoryDTO;
import com.dh.message.mapper.TaskMonitorHistoryMapper;
import com.dh.message.service.TaskMonitorHistoryService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

/**
 * <p>
 * 任务更新历史记录表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-07-24
 */
@Service
public class TaskMonitorHistoryServiceImpl extends ServiceImpl<TaskMonitorHistoryMapper, TaskMonitorHistory> implements TaskMonitorHistoryService {
    @Resource
    private ObjectMapper objectMapper;

    /**
     * 保存任务更新历史记录
     * @param taskMonitorAlertHistoryDTO 任务更新历史记录
     */
    public void addAlertHistory(TaskMonitorAlertHistoryDTO taskMonitorAlertHistoryDTO) {
        TaskMonitorHistory taskMonitorHistory = new TaskMonitorHistory();
        taskMonitorHistory.setTmId(taskMonitorAlertHistoryDTO.getTmId());
        taskMonitorHistory.setTmsId(taskMonitorAlertHistoryDTO.getTmsId());
        taskMonitorHistory.setType(taskMonitorAlertHistoryDTO.getHistoryType());
        try {
            taskMonitorHistory.setUpdateInfo(objectMapper.writeValueAsString(taskMonitorAlertHistoryDTO.toMap()));
        } catch (JsonProcessingException e) {
            //转换异常, 忽略掉
            log.error("json转换异常", e);
        }

        this.save(taskMonitorHistory);
    }
}
