package com.dh.message.service.impl.strategy;

import cn.hutool.core.util.EnumUtil;
import com.dh.common.util.R;
import com.dh.dto.bean.dto.messagerelay.ToPushGroupDTO;
import com.dh.dto.enums.GroupRobotEnum;
import com.dh.dto.feign.WeComFeignService;
import com.dh.message.bean.bo.NotificationConfig;
import com.dh.message.bean.bo.NotifyTarget;
import com.dh.message.dto.TaskMonitorInfoDTO;
import com.dh.message.enums.NotificationTypeEnum;
import com.dh.message.service.GroupNotificationStrategy;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Arrays;
import java.util.List;
import java.util.concurrent.Future;
import java.util.stream.Collectors;

/**
 * 企业微信群通知
 *
 * <AUTHOR>
 * @since 2025/8/2
 */
@Service
@Slf4j
public class WechatGroupNotificationStrategy implements GroupNotificationStrategy {
    @Resource
    private WeComFeignService weComFeignService;

    @Override
    public String getType() {
        return NotificationTypeEnum.TYPE_WECHAT_GROUP.getName();
    }

    @Override
    public Future<Boolean> send(String template, List<NotifyTarget> notifyTargets,
                                TaskMonitorInfoDTO taskMonitorInfoDTO, NotificationConfig notificationConfig) {
        try {
            //根据配置里字符串创建enum. 采用hutool的 enum工具类实现
            GroupRobotEnum anEnum = EnumUtil.fromString(GroupRobotEnum.class,
                    notificationConfig.getParams().getOrDefault("group_robot", "TEST_ROBOT").toString());
            ToPushGroupDTO toPushGroupDTO = new ToPushGroupDTO();
            toPushGroupDTO.setContent(template);
            toPushGroupDTO.setGroupRobotEnum(anEnum);
            List<Long> users = Arrays.stream(taskMonitorInfoDTO.getRelatedUid().split(",")).map(Long::parseLong).collect(Collectors.toList());
            toPushGroupDTO.setUserIdList(users);
            R<Integer> res = this.weComFeignService.toPushGroup(toPushGroupDTO);

            log.info("发送微信群通知结果: {} - {}", users, res);
        } catch (Exception e) {
            log.error("发送微信群通知异常: {}", e.getMessage());
        }

        return null;
    }

    @Override
    public boolean validate(List<NotifyTarget> notifyTarget) {
        return true;
    }
}
