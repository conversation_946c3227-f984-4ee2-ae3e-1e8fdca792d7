package com.dh.message.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.dh.common.dto.SysUserDTO;
import com.dh.framework.security.exception.BusinessException;
import com.dh.framework.security.util.SecurityUtil;
import com.dh.message.bean.bo.AlarmLevelConfig;
import com.dh.message.bean.bo.NotifyTarget;
import com.dh.message.bean.bo.RelatedNotificationConfig;
import com.dh.message.bean.entity.TaskMonitorSetting;
import com.dh.message.bean.fm.TaskMonitorSettingFM;
import com.dh.message.bean.fm.TaskMonitorSettingQueryFM;
import com.dh.message.bean.vo.MonitorRemindVO;
import com.dh.message.bean.vo.NotifyVO;
import com.dh.message.bean.vo.TaskMonitorSettingCardVO;
import com.dh.message.bean.vo.TaskMonitorSettingVO;
import com.dh.message.enums.LevelUnitEnum;
import com.dh.message.mapper.TaskMonitorSettingMapper;
import com.dh.message.service.TaskMonitorSettingService;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import java.util.ArrayList;
import java.util.Comparator;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <p>
 * 任务监控配置表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-07-24
 */
@Service
public class TaskMonitorSettingServiceImpl extends ServiceImpl<TaskMonitorSettingMapper, TaskMonitorSetting> implements TaskMonitorSettingService {

    @Override
    public Long saveOrUpdateTaskMonitorSetting(TaskMonitorSettingFM fm) {
        TaskMonitorSetting entity = new TaskMonitorSetting();
        BeanUtils.copyProperties(fm, entity);
        SysUserDTO user = SecurityUtil.getUser();
        if(user != null){
            entity.setCreateByName(user.getUsername());
        }
        this.saveOrUpdate(entity);
        return entity.getId();
    }

    @Override
    public TaskMonitorSettingVO getTaskMonitorSettingDetail(Long id) {
        TaskMonitorSetting entity = baseMapper.getTaskMonitorSettingDetail(id);
        if (entity == null) {
            throw new BusinessException("记录不存在");
        }
        TaskMonitorSettingVO taskMonitorSettingVO = new TaskMonitorSettingVO();
        BeanUtils.copyProperties(entity, taskMonitorSettingVO);
        return taskMonitorSettingVO;
    }

    @Override
    public List<TaskMonitorSettingVO> getTaskMonitorSettingList(TaskMonitorSettingQueryFM queryFM) {
        List<TaskMonitorSetting> taskMonitorSettingList = baseMapper.getTaskMonitorSettingList(queryFM);
        return taskMonitorSettingList.stream().map(entity -> {
            TaskMonitorSettingVO taskMonitorSettingVO = new TaskMonitorSettingVO();
            BeanUtils.copyProperties(entity, taskMonitorSettingVO);
            if (taskMonitorSettingVO.getRelatedNotification() == null) {
                taskMonitorSettingVO.setRelatedNotification(new RelatedNotificationConfig());
            }
            return taskMonitorSettingVO;
        }).collect(Collectors.toList());
    }

    /**
     * 获取所有的配置
     * @Author: zgj
     * @Date: 2025/7/29 上午9:52
     * @param id id
     * @return: com.dh.message.bean.entity.TaskMonitorSetting
     **/
    @Override
    public TaskMonitorSetting getAllById(Long id) {
        return baseMapper.getAllById(id);
    }

    @Override
    public TaskMonitorSettingCardVO getTaskMonitorSettingByBusinessType(String businessType) {
        TaskMonitorSettingCardVO cardVO = new TaskMonitorSettingCardVO();
        TaskMonitorSetting taskMonitorSettingVO = baseMapper.getTaskMonitorSettingByBusinessType(businessType);
        List<AlarmLevelConfig> alarmLevels = taskMonitorSettingVO.getAlarmLevels();
        List<MonitorRemindVO> notifyToList = new ArrayList<>();
        List<NotifyVO> liabilityList = new ArrayList<>();
        if(!CollectionUtils.isEmpty(alarmLevels)){
            alarmLevels = alarmLevels.stream().sorted(Comparator.comparing(AlarmLevelConfig::getLevel).reversed()).collect(Collectors.toList());
            for (AlarmLevelConfig alarmLevel : alarmLevels) {
                MonitorRemindVO monitorRemindVO = new MonitorRemindVO();
                monitorRemindVO.setLevel(alarmLevel.getName());
                String format = String.format("%s(%s%s%s)",
                        taskMonitorSettingVO.getTitle(),
                        alarmLevel.getDay() > 0 ? "提前" : "超期",
                        Math.abs(alarmLevel.getDay()),
                        getUnitChinese(alarmLevel.getUnit())
                );
                monitorRemindVO.setContent(format);
                monitorRemindVO.setRuleExplain(StringUtils.hasText(alarmLevel.getRuleExplain())?alarmLevel.getRuleExplain():"");
                List<NotifyTarget> notifyTo = alarmLevel.getNotifyTo();
                if(!CollectionUtils.isEmpty(notifyTo)) {
                    String remindScale = notifyTo.stream().map(NotifyTarget::getName).collect(Collectors.joining("、"));
                    monitorRemindVO.setRemindScale(remindScale);
                    for (NotifyTarget notifyTarget : notifyTo) {
                        NotifyVO notifyVO = new NotifyVO();
                        notifyVO.setName(notifyTarget.getName());
                        notifyVO.setUserName(
                                StringUtils.hasText(notifyTarget.getExplain())?notifyTarget.getExplain()
                                        :StringUtils.hasText(notifyTarget.getUserName())?notifyTarget.getUserName()
                                        :""
                        );
                        liabilityList.add(notifyVO);
                    }
                }
                notifyToList.add(monitorRemindVO);
            }
        }
        List<NotifyVO> mergedLiabilityList = liabilityList.stream()
                .collect(Collectors.groupingBy(
                        NotifyVO::getName,
                        LinkedHashMap::new,  // 使用LinkedHashMap保持顺序
                        Collectors.toList()
                ))
                .entrySet().stream()
                .map(entry -> {
                    NotifyVO mergedNotifyVO = new NotifyVO();
                    mergedNotifyVO.setName(entry.getKey());
                    // 将相同name的userName用"、"拼接
                    String mergedUserName = entry.getValue().stream()
                            .map(NotifyVO::getUserName)
                            .filter(StringUtils::hasText)
                            .distinct() // 去除重复的userName
                            .collect(Collectors.joining("、"));
                    mergedNotifyVO.setUserName(mergedUserName);
                    return mergedNotifyVO;
                })
                .collect(Collectors.toList());
        cardVO.setTitle(taskMonitorSettingVO.getTitle());
        cardVO.setExplain(StringUtils.hasText(taskMonitorSettingVO.getExplain())?taskMonitorSettingVO.getExplain():"");
        cardVO.setNotifyToList(notifyToList);
        cardVO.setLiabilityList(mergedLiabilityList);
        return cardVO;
    }

    private String getUnitChinese(String unit){
        if (unit == null) {
            unit = LevelUnitEnum.DAYS.getName();
        }
        return LevelUnitEnum.valueOf(unit).getName();
    }

}
