package com.dh.message.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.dh.message.mapper.SysUserMapper;
import com.dh.message.entity.SysUser;
import com.dh.message.service.ISysUserService;
import lombok.AllArgsConstructor;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <p>
 * 系统用户 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-8-4
 */
@AllArgsConstructor
@Service
public class SysUserServiceImpl extends ServiceImpl<SysUserMapper, SysUser> implements ISysUserService {

    @Override
    public List<String> getEmailsByUids(List<Long> uids) {
        return this.baseMapper.getEmailsByUids(uids);
    }
}
