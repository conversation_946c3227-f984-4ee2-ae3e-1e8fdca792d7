package com.dh.message.service.impl;

import com.dh.dto.bean.dto.message.QueueAlertDTO;
import com.dh.message.bean.bo.*;
import com.dh.message.constant.AlertFlagConstant;
import com.dh.message.dto.TaskMonitorInfoDTO;
import com.dh.message.dto.TriggerAlertDTO;
import com.dh.message.enums.NotificationTypeEnum;
import com.dh.message.listener.TaskMonitorEventPublisher;
import com.dh.message.service.*;
import com.dh.message.util.TemplateRenderUtil;
import com.google.common.collect.Sets;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.lang.reflect.Field;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 通知实现类
 *
 * <AUTHOR>
 * @since 2025-07-25
 */
@Service
@Slf4j
public class NotificationImpl implements Notification {
    /**
     * 关联用户字段名称
     */
    public static final String RELATED_UID_FIELD = "relatedUid";

    @Resource
    private NotificationStrategyFactory strategyFactory;

    @Resource
    private GroupNotificationStrategyFactory groupStrategyFactory;

    @Resource
    private TaskMonitorEventPublisher taskMonitorEventPublisher;

    /**
     * 发送通知
     */
    @Override
    public List<Long> send(TriggerAlertDTO triggerAlert) {
        try {
            validateTriggerAlert(triggerAlert);

            int warningLevel = calculateWarningLevel(triggerAlert);
            TaskMonitorInfoDTO taskInfo = triggerAlert.getTaskMonitorInfoDTO();

            // 获取匹配的通知配置
            List<NotificationConfig> matchingNotifications = findMatchingNotifications(taskInfo, triggerAlert.getLevel());

            // 获取匹配的告警级别配置
            List<AlarmLevelConfig> matchingAlarmLevels = findMatchingAlarmLevels(taskInfo.getAlarmLevels(), triggerAlert.getLevel());
            List<Long> list = new ArrayList<>();
            Map<String, String> params = objectToMap(taskInfo);
            matchingAlarmLevels.forEach(alarmLevel -> Optional.ofNullable(alarmLevel.getNotifyTo()).orElse(Collections.emptyList()).forEach(notifyTarget -> {
                if (!Objects.equals(notifyTarget.getField(), RELATED_UID_FIELD)) {  //去掉直接负责人id
                    list.addAll(notifyTarget.getUids(params));
                }
            }));
            log.debug("Send user list is:{}", list);

            // 处理每个通知配置
            matchingNotifications.forEach(notificationConfig -> processNotificationConfig(notificationConfig, matchingAlarmLevels, taskInfo, warningLevel, triggerAlert.getLevel(), triggerAlert.getIsWarning()));
            return new ArrayList<>(Sets.newLinkedHashSet(list));
        } catch (Exception e) {
            log.error("发送通知失败: {}", e.getMessage(), e);
            throw new RuntimeException("通知发送失败", e);
        }
    }

    /**
     * 验证触发告警参数
     */
    private void validateTriggerAlert(TriggerAlertDTO triggerAlert) {
        if (triggerAlert == null) {
            throw new IllegalArgumentException("触发告警参数不能为空");
        }
        if (triggerAlert.getTaskMonitorInfoDTO() == null) {
            throw new IllegalArgumentException("任务监控信息不能为空");
        }
        if (triggerAlert.getLevel() < 1 || triggerAlert.getLevel() > AlertFlagConstant.MAX_LEVEL) {
            throw new IllegalArgumentException("告警级别必须在1-" + AlertFlagConstant.MAX_LEVEL + "之间");
        }
    }

    /**
     * 计算预警级别
     * 人员选择预警需要报警上一个级别, 报警直接报本级别
     */
    private int calculateWarningLevel(TriggerAlertDTO triggerAlert) {
        return triggerAlert.getIsWarning() ? Math.min(triggerAlert.getLevel() + 1, AlertFlagConstant.MAX_LEVEL) : triggerAlert.getLevel();
    }

    /**
     * 查找匹配的通知配置
     */
    private List<NotificationConfig> findMatchingNotifications(TaskMonitorInfoDTO taskInfo, int level) {
        return Optional.ofNullable(taskInfo.getNotifications()).orElse(Collections.emptyList()).stream().filter(notification -> isValidNotificationConfig(notification, level)).collect(Collectors.toList());
    }

    /**
     * 验证通知配置是否有效
     */
    private boolean isValidNotificationConfig(NotificationConfig notification, int level) {
        return notification != null && notification.getTemplates() != null && notification.getTemplates().stream().anyMatch(template -> template.getLevel() == level);
    }

    /**
     * 处理单个通知配置
     */
    private void processNotificationConfig(NotificationConfig notificationConfig, List<AlarmLevelConfig> matchingAlarmLevels, TaskMonitorInfoDTO taskInfo, int warningLevel, int currentLevel, boolean isWarning) {
        log.debug("处理通知配置: {}", notificationConfig.getType());

        // 获取匹配的模板
        List<NotificationTemplate> matchingTemplates = findMatchingTemplates(notificationConfig.getTemplates(), currentLevel);

        // 处理模板和告警级别的组合
        matchingTemplates.forEach(template -> matchingAlarmLevels.forEach(alarmLevel -> processTemplateAndAlarmLevel(template, alarmLevel, notificationConfig, taskInfo, isWarning)));
    }

    /**
     * 查找匹配的模板
     */
    private List<NotificationTemplate> findMatchingTemplates(List<NotificationTemplate> templates, int level) {
        return Optional.ofNullable(templates).orElse(Collections.emptyList()).stream().filter(template -> template.getLevel() == level).collect(Collectors.toList());
    }

    /**
     * 查找匹配的告警级别配置
     */
    private List<AlarmLevelConfig> findMatchingAlarmLevels(List<AlarmLevelConfig> alarmLevels, int level) {
        return Optional.ofNullable(alarmLevels).orElse(Collections.emptyList()).stream().filter(alarmLevel -> alarmLevel.getLevel() == level).collect(Collectors.toList());
    }

    public static Map<String, String> objectToMap(Object obj) {
        Map<String, String> map = new HashMap<>();
        if (obj == null) {
            return map;
        }

        Class<?> clazz = obj.getClass();
        Field[] fields = clazz.getDeclaredFields();

        for (Field field : fields) {
            field.setAccessible(true);
            try {
                Object value = field.get(obj);
                if (value != null) {
                    map.put(field.getName(), value.toString());
                }
            } catch (IllegalAccessException e) {
                // 处理异常
                log.error("Error accessing field: {}", field.getName(), e);
            }
        }

        return map;
    }


    /**
     * 获取模板参数映射表
     *
     * @param taskInfo   任务监控信息DTO对象，包含任务的基本信息和扩展参数
     * @param alarmLevel 告警级别配置对象，包含告警级别的相关信息
     * @return 包含模板参数的Map，key为参数名，value为参数值
     */
    private Map<String, String> getTemplateParams(TaskMonitorInfoDTO taskInfo, AlarmLevelConfig alarmLevel) {
        return Optional.ofNullable(taskInfo).map(info -> {
            Map<String, Object> extparams = Optional.ofNullable(info.getExtParams()).orElseGet(HashMap::new);
            Map<String, String> params = new HashMap<>();
            extparams.forEach((key, obj) -> params.put(key, obj.toString()));
            Optional.ofNullable(alarmLevel).ifPresent(level -> params.put("#level_name", Optional.ofNullable(level.getName()).orElse("")));

            if (alarmLevel != null) {
                params.put("#level", alarmLevel.getLevel().toString());
            } else {
                params.put("#level", "");
            }
            Map<String, LocalDateTime> dateNodes = taskInfo.getDateNodes();
            dateNodes.forEach((key, item) -> {
                if (item == null) {
                    params.put("#" + key, "");
                } else {
                    params.put("#" + key, item.format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")));
                }
            });
            params.put("#monitor_title", Optional.ofNullable(info.getTitle()).orElse(""));
            params.put("#task_content", Optional.ofNullable(info.getTaskContent()).orElse(""));
            params.put("#end_date", info.getEndDate().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")));
            params.put("#start_date", info.getStartDate().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")));

            return params;
        }).orElse(Collections.emptyMap());
    }


    /**
     * 处理模板和告警级别的组合
     *
     * @param template           通知模板对象，包含消息模板内容
     * @param alarmLevel         告警级别配置，包含通知目标等配置信息
     * @param notificationConfig 通知配置对象
     * @param taskInfo           任务监控信息DTO对象，包含任务相关参数
     * @param isWarning          是否为预警状态的标识
     */
    private void processTemplateAndAlarmLevel(NotificationTemplate template, AlarmLevelConfig alarmLevel, NotificationConfig notificationConfig, TaskMonitorInfoDTO taskInfo, boolean isWarning) {
        // 渲染模板消息内容，根据是否预警状态选择不同的消息模板
        String templateMessage = TemplateRenderUtil.render(isWarning ? template.getPreMessage() : template.getMessage(), getTemplateParams(taskInfo, alarmLevel));

        //队列配置不为空
        if (alarmLevel.getQueues() != null && !alarmLevel.getQueues().isEmpty()) {
            log.info("Trigger queue alert event level {},{} - {}", alarmLevel.getLevel(), isWarning, alarmLevel.getQueues());
            QueueAlertDTO queueAlertDTO = QueueAlertDTO.builder()
                    .level(alarmLevel.getLevel())
                    .isWarning(isWarning)
                    .tmId(taskInfo.getId())
                    .tmsId(taskInfo.getTmsId())
                    .businessId(taskInfo.getBusinessId())
                    .businessExtId(taskInfo.getBusinessExtId())
                    .queues(alarmLevel.getQueues())
                    .build();

            taskMonitorEventPublisher.triggerQueueAlert(queueAlertDTO);
        }

        NotificationTypeEnum typeEnum = NotificationTypeEnum.fromCode(notificationConfig.getType());

        if (typeEnum.getIsGroup()) {
            sendGroupNotification(notificationConfig, templateMessage, alarmLevel.getNotifyTo(), taskInfo);
        } else {
            // 遍历告警级别配置中的通知目标，发送通知消息
            Set<Long> notifyUids = new HashSet<>();
            Optional.ofNullable(alarmLevel.getNotifyTo()).orElse(Collections.emptyList()).forEach(notifyTarget -> {
                sendNotification(notificationConfig, templateMessage, notifyTarget, taskInfo, notifyUids);
            });
            notifyUids.clear();
        }
    }

    /**
     * 按组发送通知
     *
     * @param notificationConfig Notification配置
     * @param template           消息内容
     * @param notifyTarget       通知目标列表
     * @param taskMonitorInfoDTO 任务信息
     */
    public void sendGroupNotification(NotificationConfig notificationConfig, String template,
                                      List<NotifyTarget> notifyTarget,
                                      TaskMonitorInfoDTO taskMonitorInfoDTO) {
        log.info("异步通知组发送开始 - 类型: {}, To: {}", notificationConfig.getType(), notifyTarget);
        try {
            String notificationType = notificationConfig.getType().toLowerCase();
            //检查是否支持该通知类型
            if (!groupStrategyFactory.supports(notificationType)) {
                log.warn("未实现的组通知类型: {}", notificationType);
                return;
            }

            // 获取对应的策略
            GroupNotificationStrategy strategy = groupStrategyFactory.getStrategy(notificationType);
            if (!strategy.validate(notifyTarget)) {
                log.warn("通知组参数验证失败: 类型={}, 目标={}", notificationType, notifyTarget);
                return;
            }

            strategy.send(template, notifyTarget, taskMonitorInfoDTO, notificationConfig);
        } catch (Exception e) {
            log.error("发送组通知异常: 类型={}, 目标={}", notificationConfig.getType(), notifyTarget, e);
        }
    }

    /**
     * 发送具体通知
     */
    public void sendNotification(NotificationConfig notificationConfig, String template, NotifyTarget notifyTarget,
                                 TaskMonitorInfoDTO taskMonitorInfoDTO, Set<Long> notifyUids) {
        log.info("异步通知发送开始 - 类型: {}, To: {}", notificationConfig.getType(), notifyTarget);
        try {
            String notificationType = notificationConfig.getType().toLowerCase();

            // 检查是否支持该通知类型
            if (!strategyFactory.supports(notificationType)) {
                log.warn("未实现的通知类型: {}", notificationType);
                return;
            }

            // 获取对应的策略
            NotificationStrategy strategy = strategyFactory.getStrategy(notificationType);

            // 验证参数
            if (!strategy.validate(notifyTarget)) {
                log.warn("通知参数验证失败: 类型={}, 目标={}", notificationType, notifyTarget);
                return;
            }

            // 发送通知
            Map<String, String> params = objectToMap(taskMonitorInfoDTO);
            notifyTarget.getUids(params).forEach(uid -> {
                if (!notifyUids.contains(uid)) {
                    strategy.send(template, NotifyTo.builder().field(notifyTarget.getField()).name(notifyTarget.getName()).score(notifyTarget.getScore()).uid(uid).build(), taskMonitorInfoDTO, notificationConfig);

                    notifyUids.add(uid);
                }
            });
        } catch (Exception e) {
            log.error("发送通知异常: 类型={}, 目标={}", notificationConfig.getType(), notifyTarget, e);
        }
    }
}
