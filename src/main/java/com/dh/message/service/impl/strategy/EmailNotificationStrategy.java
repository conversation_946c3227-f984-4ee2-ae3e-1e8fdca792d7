package com.dh.message.service.impl.strategy;

import com.dh.dto.bean.dto.messagerelay.SendEmailDTO;
import com.dh.dto.feign.MailFeignService;
import com.dh.message.bean.bo.NotificationConfig;
import com.dh.message.bean.bo.NotifyTarget;
import com.dh.message.bean.bo.NotifyTo;
import com.dh.message.dto.TaskMonitorInfoDTO;
import com.dh.message.enums.NotificationTypeEnum;
import com.dh.message.service.GroupNotificationStrategy;
import com.dh.message.service.ISysUserService;
import com.dh.message.util.TemplateRenderUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.scheduling.annotation.Async;
import org.springframework.scheduling.annotation.AsyncResult;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.concurrent.Future;
import java.util.stream.Collectors;

import static com.dh.message.service.impl.NotificationImpl.objectToMap;

/**
 * 邮件通知策略实现
 *
 * <AUTHOR>
 * @version 0.9.1
 * @since 2025/7/25
 */
@Service
@Slf4j
public class EmailNotificationStrategy implements GroupNotificationStrategy {
    @Resource
    ISysUserService sysUserService;
    @Resource
    private MailFeignService mailFeignService;

    @Override
    public String getType() {
        return NotificationTypeEnum.TYPE_EMAIL.getName();
    }

    @Override
    @Async("sendPoolExecutor")
    public Future<Boolean> send(String template, List<NotifyTarget> notifyTargets, TaskMonitorInfoDTO taskMonitorInfoDTO, NotificationConfig notificationConfig) {
        try {
            log.info("发送邮件通知: 模板={}, 目标={}", template, notifyTargets);

            Map<String, String> params = objectToMap(taskMonitorInfoDTO);
            List<Long> toUsers = new ArrayList<>();
            List<Long> ccUsers = new ArrayList<>();
            notifyTargets.forEach(notifyTarget -> {
                if (notifyTarget.getField().isEmpty()) toUsers.addAll(notifyTarget.getUids(params));
                else ccUsers.addAll(notifyTarget.getUids(params));
            });
            List<String> toUserEmails = sysUserService.getEmailsByUids(toUsers).stream().filter(email -> !email.isEmpty()).collect(Collectors.toList());
            List<String> ccUserEmails =
                    sysUserService.getEmailsByUids(ccUsers).stream().filter(email -> !email.isEmpty()).collect(Collectors.toList());
            
            SendEmailDTO sendEmailDTO = new SendEmailDTO();
            sendEmailDTO.setTableId(taskMonitorInfoDTO.getId());
            sendEmailDTO.setSubject("报警");
            sendEmailDTO.setText(template);
            sendEmailDTO.setTo(toUserEmails);
            sendEmailDTO.setCc(ccUserEmails);

            log.info("发送邮件通知 {}, {}={}", sendEmailDTO, toUsers, ccUsers);
            //mailFeignService.sendEmail(sendEmailDTO);
            // 模拟发送成功
            return AsyncResult.forValue(true);
        } catch (Exception e) {
            log.error("发送邮件通知失败: 模板={}, 目标={}", template, notifyTargets, e);
            return AsyncResult.forValue(false);
        }
    }

    @Override
    public boolean validate(List<NotifyTarget> notifyTarget) {
        if (notifyTarget == null) {
            log.warn("邮件通知目标为空");
            return false;
        }

        return true;
    }
} 