package com.dh.message.service;

import com.dh.message.bean.entity.TaskMonitorHistory;
import com.baomidou.mybatisplus.extension.service.IService;
import com.dh.message.dto.TaskMonitorAlertHistoryDTO;

/**
 * <p>
 * 任务更新历史记录表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-07-24
 */
public interface TaskMonitorHistoryService extends IService<TaskMonitorHistory> {
    void addAlertHistory(TaskMonitorAlertHistoryDTO taskMonitorAlertHistoryDTO);
}
