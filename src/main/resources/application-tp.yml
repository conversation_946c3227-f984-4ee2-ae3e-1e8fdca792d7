spring:
  rabbitmq:
    host: ${DOCKER_RABBITMQ_HOST:}
    port: ${DOCKER_RABBITMQ_PORT:}
    username: ${DOCKER_RABBITMQ_USERNAME:}
    password: ${DOCKER_RABBITMQ_PASSWORD:}
    virtual-host: ${DOCKER_RABBITMQ_VIRTUAL_HOST:}
  datasource:
    type: com.zaxxer.hikari.HikariDataSource
    driver-class-name: com.mysql.cj.jdbc.Driver
    url: jdbc:mysql://${DOCKER_MYSQL_HOST:}/${DOCKER_MYSQL_PLATFORM_NAME:}?zeroDateTimeBehavior=CONVERT_TO_NULL&allowMultiQueries=true&useUnicode=true&characterEncoding=UTF8&useSSL=false&serverTimezone=GMT%2B8
    username: ${DOCKER_MYSQL_PLATFORM_USERNAME:}
    password: ${DOCKER_MYSQL_PLATFORM_PASSWORD:}
    hikari:
      pool-name: ${spring.application.name}
      minimum-idle: 10
      idle-timeout: 600000
      maximum-pool-size: 30
      max-lifetime: 1800000
      auto-commit: true
      connection-timeout: 30000
      validation-timeout: 5000
      connection-test-query: SELECT 1
      data-source-properties:
        cachePrepStmts: true
        prepStmtCacheSize: 500
        prepStmtCacheSqlLimit: 2048
        useLocalSessionState: true
        rewriteBatchedStatements: true
        cacheResultSetMetadata: true
        cacheServerConfiguration: true
        elideSetAutoCommits: true
        maintainTimeStats: false
  jackson:
    date-format: yyyy-MM-dd HH:mm:ss
    time-zone: GMT+8
  servlet:
    multipart:
      max-request-size: 100MB
      max-file-size: 20MB
  redis:
    port: ${DOCKER_REDIS_PORT:}
    host: ${DOCKER_REDIS_HOST:}
    password: ${DOCKER_REDIS_PASS:}
#  mail:
#    default-encoding: UTF-8
#    host: ${DOCKER_MAIL_HOST:}
#    port: ${DOCKER_MAIL_PORT:}
#    protocol: smtp
#    username: ${DOCKER_MAIL_USERNAME:}
#    password: ${DOCKER_MAIL_PASSWORD:}
#    properties:
#      mail:
#        smtp:
#          auth: true # 使用
#          starttls: # 使用 SSL 安全协议，须如下配置
#            enable: true
#            required: true
#          socketFactory:
#            port: 994
#            class: javax.net.ssl.SSLSocketFactory
#            fallback: false

# 加密算法和规则设置
jasypt:
  encryptor:
    password: ${DH_JASYPT_ENCRYPTOR_PASSWORD}
    algorithm: PBEWithMD5AndDES
    property:
      prefix: ENC(
      suffix: )

#mybatis-plus 配置
mybatis-plus:
  #sql打印配置
  configuration:
    log-impl: org.apache.ibatis.logging.stdout.StdOutImpl
  mapper-locations: classpath*:/mapper/*.xml

# 日志配置
logging:
  level:
    root: info
    com.dh.*: info
  file:
    name: ./log/${spring.application.name}.log
    #单个日志文件大小 MB  GB
    max-size: 20MB
    #日志文件保留天数
    max-history: 7
    #所有文件最多不超过 MB GB
    total-size-cap: 200MB

#系统配置
system-config:
  #消息最大推送次数
  max-send-num: 3
  #失败消息推送间隔时间 单位：分钟（失败次数*fail_send_time）
  fail-send-time: 10

#阿里云短信服务配置
aliyun-sms-config:
  default-connect-timeout: 10000
  default-read_timeout: 10000
  access_key_id: ${DOCKER_SMS_ACCESS_KEY_ID:}
  access_key_secret: ${DOCKER_SMS_ACCESS_KEY_SECRET:}
#  sign_name: ${DOCKER_SMS_SIGN_NAME:}

dh:
  log:
    project: ${DOCKER_LOG_PROJECT:}
    endpoint: ${DOCKER_LOG_ENDPOINT:}   # 内网访问
    accessKeyId: ${DOCKER_ACCESS_KEY_ID:}              #accessKeyId
    accessKeySecret: ${DOCKER_ACCESS_KEY_SECRET:}    #accessKeySecret
    logStore: ${DOCKER_LOG_URL_STORE:}  # 接口日志
    dmlLogStore: ${DOCKER_LOG_DML_STORE:} # dml日志（不填则不记录）
  dto:
    server:
      url: ${DOCKER_INNER_CALL_URL:}
  framework:
    security:
      server:
        white-ips: ${DOCKER_INNER_CALL_IP:}